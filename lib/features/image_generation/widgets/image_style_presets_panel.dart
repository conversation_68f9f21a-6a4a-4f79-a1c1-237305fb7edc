import 'package:flutter/material.dart';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_style_presets.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

/// Widget for displaying and selecting style presets
///
/// Provides an organized interface for users to browse and select
/// from various style categories and specific styles for image generation.
class ImageStylePresetsPanel extends StatelessWidget {
  /// Currently selected style category
  final StyleCategory? selectedStyleCategory;

  /// Currently selected specific style
  final String? selectedStyle;

  /// Callback when a style category is selected
  final ValueChanged<StyleCategory?>? onStyleCategoryChanged;

  /// Callback when a specific style is selected
  final ValueChanged<String?>? onStyleChanged;

  /// Whether the panel is expanded/visible
  final bool isVisible;

  const ImageStylePresetsPanel({
    super.key,
    this.selectedStyleCategory,
    this.selectedStyle,
    this.onStyleCategoryChanged,
    this.onStyleChanged,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    final theme = Theme.of(context);

    return CardStyles.outlined(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 16),
          _buildStyleCategories(theme),
          if (selectedStyleCategory != null) ...[
            const SizedBox(height: 16),
            _buildSpecificStyles(theme),
          ],
        ],
      ),
    );
  }

  /// Builds the panel header
  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(Icons.palette, color: AppTheme.primaryGreen, size: 20),
        const SizedBox(width: 8),
        Text(
          'Style Presets',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const Spacer(),
        if (selectedStyleCategory != null || selectedStyle != null)
          TextButton(
            onPressed: () {
              onStyleCategoryChanged?.call(null);
              onStyleChanged?.call(null);
            },
            child: Text(
              'Clear',
              style: TextStyle(color: AppTheme.secondaryGreen),
            ),
          ),
      ],
    );
  }

  /// Builds the style category selection chips
  Widget _buildStyleCategories(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: StyleCategory.values.map((category) {
            final isSelected = selectedStyleCategory == category;
            return FilterChip(
              label: Text(ImageStylePresets.getCategoryDisplayName(category)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  onStyleCategoryChanged?.call(category);
                  // Clear specific style when category changes
                  onStyleChanged?.call(null);
                } else {
                  onStyleCategoryChanged?.call(null);
                  onStyleChanged?.call(null);
                }
              },
              selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
              checkmarkColor: AppTheme.primaryGreen,
              labelStyle: TextStyle(
                color: isSelected ? AppTheme.primaryGreen : null,
                fontWeight: isSelected ? FontWeight.w600 : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Builds the specific style selection for the selected category
  Widget _buildSpecificStyles(ThemeData theme) {
    final styles = ImageStylePresets.getStylesForCategory(selectedStyleCategory!);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${ImageStylePresets.getCategoryDisplayName(selectedStyleCategory!)} Styles',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          constraints: const BoxConstraints(maxHeight: 200),
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // "Any" option to use category without specific style
                FilterChip(
                  label: Text('Any ${ImageStylePresets.getCategoryDisplayName(selectedStyleCategory!)}'),
                  selected: selectedStyle == null,
                  onSelected: (selected) {
                    if (selected) {
                      onStyleChanged?.call(null);
                    }
                  },
                  selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
                  checkmarkColor: AppTheme.secondaryGreen,
                  labelStyle: TextStyle(
                    color: selectedStyle == null ? AppTheme.secondaryGreen : null,
                    fontWeight: selectedStyle == null ? FontWeight.w600 : null,
                    fontSize: 12,
                  ),
                ),
                // Specific styles
                ...styles.map((style) {
                  final isSelected = selectedStyle == style;
                  return FilterChip(
                    label: Text(style),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        onStyleChanged?.call(style);
                      } else {
                        onStyleChanged?.call(null);
                      }
                    },
                    selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                    checkmarkColor: AppTheme.primaryGreen,
                    labelStyle: TextStyle(
                      color: isSelected ? AppTheme.primaryGreen : null,
                      fontWeight: isSelected ? FontWeight.w600 : null,
                      fontSize: 12,
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// Compact style selector for use in input areas
class CompactStyleSelector extends StatelessWidget {
  /// Currently selected style category
  final StyleCategory? selectedStyleCategory;

  /// Currently selected specific style
  final String? selectedStyle;

  /// Callback when style selection changes
  final ValueChanged<StyleCategory?>? onStyleCategoryChanged;

  /// Callback when specific style changes
  final ValueChanged<String?>? onStyleChanged;

  const CompactStyleSelector({
    super.key,
    this.selectedStyleCategory,
    this.selectedStyle,
    this.onStyleCategoryChanged,
    this.onStyleChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        // Style category dropdown
        Expanded(
          child: DropdownButtonFormField<StyleCategory>(
            value: selectedStyleCategory,
            decoration: InputDecoration(
              labelText: 'Style Category',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            items: [
              const DropdownMenuItem<StyleCategory>(
                value: null,
                child: Text('None'),
              ),
              ...StyleCategory.values.map((category) {
                return DropdownMenuItem<StyleCategory>(
                  value: category,
                  child: Text(ImageStylePresets.getCategoryDisplayName(category)),
                );
              }).toList(),
            ],
            onChanged: (value) {
              onStyleCategoryChanged?.call(value);
              // Clear specific style when category changes
              if (value != selectedStyleCategory) {
                onStyleChanged?.call(null);
              }
            },
          ),
        ),

        const SizedBox(width: 8),

        // Specific style dropdown (only shown if category is selected)
        if (selectedStyleCategory != null)
          Expanded(
            child: DropdownButtonFormField<String>(
              value: selectedStyle,
              decoration: InputDecoration(
                labelText: 'Specific Style',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('Any'),
                ),
                ...ImageStylePresets.getStylesForCategory(selectedStyleCategory!).map((style) {
                  return DropdownMenuItem<String>(
                    value: style,
                    child: Text(style),
                  );
                }).toList(),
              ],
              onChanged: onStyleChanged,
            ),
          ),
      ],
    );
  }
}

/// Style preview chip showing current selection
class StylePreviewChip extends StatelessWidget {
  /// Current image generation settings
  final ImageGenerationSettings settings;

  /// Callback when user wants to clear style
  final VoidCallback? onClear;

  const StylePreviewChip({
    super.key,
    required this.settings,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    if (!settings.hasStyleSettings) {
      return const SizedBox.shrink();
    }

    return Chip(
      label: Text(
        settings.activeSettingsDescription,
        style: const TextStyle(fontSize: 12),
      ),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onClear,
      backgroundColor: AppTheme.primaryGreen.withValues(alpha: 0.1),
      deleteIconColor: AppTheme.primaryGreen,
      labelStyle: TextStyle(color: AppTheme.primaryGreen),
    );
  }
}
