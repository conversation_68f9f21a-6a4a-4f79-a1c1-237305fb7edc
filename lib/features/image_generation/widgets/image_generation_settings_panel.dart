import 'package:flutter/material.dart';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/models/image_style_presets.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

/// Widget for advanced image generation settings
///
/// Provides controls for lighting, composition, subject templates,
/// aspect ratio, image size, and other advanced generation parameters.
class ImageGenerationSettingsPanel extends StatelessWidget {
  /// Current image generation settings
  final ImageGenerationSettings settings;

  /// Callback when settings change
  final ValueChanged<ImageGenerationSettings>? onSettingsChanged;

  /// Whether the panel is expanded/visible
  final bool isVisible;

  const ImageGenerationSettingsPanel({
    super.key,
    required this.settings,
    this.onSettingsChanged,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    final theme = Theme.of(context);

    return CardStyles.outlined(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 16),
          _buildLightingMoodSection(theme),
          const SizedBox(height: 16),
          _buildCompositionSection(theme),
          const SizedBox(height: 16),
          _buildSubjectTemplateSection(theme),
          const SizedBox(height: 16),
          _buildImageParametersSection(theme),
          const SizedBox(height: 16),
          _buildAdvancedOptionsSection(theme),
        ],
      ),
    );
  }

  /// Builds the panel header
  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Icon(Icons.tune, color: AppTheme.primaryGreen, size: 20),
        const SizedBox(width: 8),
        Text(
          'Advanced Settings',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: () {
            onSettingsChanged?.call(const ImageGenerationSettings());
          },
          child: Text(
            'Reset',
            style: TextStyle(color: AppTheme.secondaryGreen),
          ),
        ),
      ],
    );
  }

  /// Builds the lighting mood selection section
  Widget _buildLightingMoodSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lighting & Mood',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            FilterChip(
              label: const Text('None'),
              selected: settings.lightingMood == null,
              onSelected: (selected) {
                if (selected) {
                  onSettingsChanged?.call(settings.copyWith(lightingMood: null));
                }
              },
              selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
              checkmarkColor: AppTheme.secondaryGreen,
            ),
            ...LightingMood.values.map((mood) {
              final isSelected = settings.lightingMood == mood;
              return FilterChip(
                label: Text(ImageStylePresets.getLightingDisplayName(mood)),
                selected: isSelected,
                onSelected: (selected) {
                  onSettingsChanged?.call(
                    settings.copyWith(lightingMood: selected ? mood : null),
                  );
                },
                selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryGreen,
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: isSelected ? AppTheme.primaryGreen : null,
                  fontWeight: isSelected ? FontWeight.w600 : null,
                ),
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  /// Builds the composition guide selection section
  Widget _buildCompositionSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Composition Guide',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            FilterChip(
              label: const Text('None'),
              selected: settings.compositionGuide == null,
              onSelected: (selected) {
                if (selected) {
                  onSettingsChanged?.call(settings.copyWith(compositionGuide: null));
                }
              },
              selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
              checkmarkColor: AppTheme.secondaryGreen,
            ),
            ...CompositionGuide.values.map((guide) {
              final isSelected = settings.compositionGuide == guide;
              return FilterChip(
                label: Text(ImageStylePresets.getCompositionDisplayName(guide)),
                selected: isSelected,
                onSelected: (selected) {
                  onSettingsChanged?.call(
                    settings.copyWith(compositionGuide: selected ? guide : null),
                  );
                },
                selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryGreen,
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: isSelected ? AppTheme.primaryGreen : null,
                  fontWeight: isSelected ? FontWeight.w600 : null,
                ),
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  /// Builds the subject template selection section
  Widget _buildSubjectTemplateSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Subject Focus',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            FilterChip(
              label: const Text('None'),
              selected: settings.subjectTemplate == null,
              onSelected: (selected) {
                if (selected) {
                  onSettingsChanged?.call(settings.copyWith(subjectTemplate: null));
                }
              },
              selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
              checkmarkColor: AppTheme.secondaryGreen,
            ),
            ...SubjectTemplate.values.map((template) {
              final isSelected = settings.subjectTemplate == template;
              return FilterChip(
                label: Text(ImageStylePresets.getSubjectDisplayName(template)),
                selected: isSelected,
                onSelected: (selected) {
                  onSettingsChanged?.call(
                    settings.copyWith(subjectTemplate: selected ? template : null),
                  );
                },
                selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryGreen,
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: isSelected ? AppTheme.primaryGreen : null,
                  fontWeight: isSelected ? FontWeight.w600 : null,
                ),
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  /// Builds the image parameters section (aspect ratio, size)
  Widget _buildImageParametersSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Image Parameters',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Aspect ratio selector
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Aspect Ratio',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<double>(
                    value: settings.aspectRatio,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 0.75, child: Text('3:4 (Portrait)')),
                      DropdownMenuItem(value: 1.0, child: Text('1:1 (Square)')),
                      DropdownMenuItem(value: 1.33, child: Text('4:3 (Landscape)')),
                      DropdownMenuItem(value: 1.78, child: Text('16:9 (Widescreen)')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        onSettingsChanged?.call(settings.copyWith(aspectRatio: value));
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // Image size selector
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quality',
                    style: theme.textTheme.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<int>(
                    value: settings.imageSize,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 512, child: Text('Standard')),
                      DropdownMenuItem(value: 1024, child: Text('High')),
                      DropdownMenuItem(value: 2048, child: Text('Ultra')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        onSettingsChanged?.call(settings.copyWith(imageSize: value));
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the advanced options section
  Widget _buildAdvancedOptionsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Advanced Options',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        
        // Enhanced prompting toggle
        SwitchListTile(
          title: const Text('Enhanced Prompting'),
          subtitle: const Text('Let AI automatically improve your prompts'),
          value: settings.useAdvancedPrompting,
          onChanged: (value) {
            onSettingsChanged?.call(settings.copyWith(useAdvancedPrompting: value));
          },
          activeColor: AppTheme.primaryGreen,
          contentPadding: EdgeInsets.zero,
        ),
        
        const SizedBox(height: 8),
        
        // Negative prompt field
        TextFormField(
          initialValue: settings.negativePrompt,
          decoration: InputDecoration(
            labelText: 'Negative Prompt (Optional)',
            hintText: 'Elements to avoid in the image...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          maxLines: 2,
          onChanged: (value) {
            onSettingsChanged?.call(settings.copyWith(negativePrompt: value));
          },
        ),
      ],
    );
  }
}
